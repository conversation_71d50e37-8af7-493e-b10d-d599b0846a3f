import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import styles from "@/pages/find-rating/findRatings.module.css";
import Pagemenu from "@/components/pagemenu/pagemenu";
import SubBanner from "@/components/innerbanner/innerbanner";
import SelectStyle from "@/components/SelectBox/select";
import Membercards from "@/components/membercards/membercards";
import { fetchPageBySlug, fetchCapclist, fetchServices } from "@/lib/api/PageBySlug";
import { fetchRatingTab } from "@/lib/api/byfield";
import Head from "next/head";
import { useRouter } from "next/router";

const Index = ({ pageData, Tabs, ratingList, serviceList }) => {
	const { locale } = useRouter();
	const langCode = locale === "ar" ? "ar" : "en";
	const banner = pageData.acf.banner_details || "";

	const [names, setNames] = useState([]);
	const [nameFilter, setNameFilter] = useState();

	const [serielnum, setSerielnum] = useState([]);
	const [serielnumv, setSerielnumv] = useState();

	const [rating, setRating] = useState([]);
	const [ratingv, setRatingv] = useState();

	const [ratingActions, setRatingActions] = useState([]);
	const [ratingActionsv, setRatingActionsv] = useState();

	const [outlooks, setOutlooks] = useState([]);
	const [outlooksv, setOutlooksv] = useState();

	const [years, setYears] = useState();

	const resultsDivRef = useRef(null);

	const [filteredResults, setFilteredResults] = useState(ratingList);
	const [currentPage, setCurrentPage] = useState(1);
	const itemsPerPage = 10;

	useEffect(() => {
		if (ratingList) {
			// Company
			const companies = [...new Set(ratingList.map(item => item.acf.company_request).filter(Boolean))]
				.map(value => ({ value, label: value }));
			companies.unshift({ value: "", label: "Choose" });
			setNames(companies);

			// Serial Numbers
			const serials = [...new Set(ratingList.map(item => item.acf.serial_numbe).filter(Boolean))]
				.map(value => ({ value, label: value }));
			serials.unshift({ value: "", label: "Choose" });
			setSerielnum(serials);

			// Long Term Ratings
			const longTerms = [...new Set(ratingList.map(item => item.acf.credit_rating?.long_term_rating).filter(Boolean))]
				.map(value => ({ value, label: value }));
			longTerms.unshift({ value: "", label: "Choose" });
			setRating(longTerms);

			// Short Term Ratings
			const shortTerms = [...new Set(ratingList.map(item => item.acf.credit_rating?.short_term_rating).filter(Boolean))]
				.map(value => ({ value, label: value }));
			shortTerms.unshift({ value: "", label: "Choose" });
			setRatingActions(shortTerms);

			// Outlooks
			const outlookVals = [...new Set(ratingList.map(item => item.acf.credit_rating?.outlook).filter(Boolean))]
				.map(value => ({ value, label: value }));
			outlookVals.unshift({ value: "", label: "Choose" });
			setOutlooks(outlookVals);
		}
	}, [ratingList]);

	const handleFilter = (event) => {
		event.preventDefault();
		const results = ratingList.filter(item => {
			const year = new Date(item.acf.credit_rating.date_of_issuance).getFullYear();
			return (
				(!nameFilter || item.acf.company_request === nameFilter) &&
				(!serielnumv || item.acf.serial_numbe === serielnumv) &&
				(!ratingv || item.acf.credit_rating.long_term_rating === ratingv) &&
				(!ratingActionsv || item.acf.credit_rating.short_term_rating === ratingActionsv) &&
				(!outlooksv || item.acf.credit_rating.outlook === outlooksv) &&
				(!years || parseInt(years) === year)
			);
		});
		setCurrentPage(1);
		setFilteredResults(results);
		resultsDivRef.current.scrollIntoView({ behavior: "smooth" });
	};

	const indexOfLastItem = currentPage * itemsPerPage;
	const indexOfFirstItem = indexOfLastItem - itemsPerPage;
	const currentItems = filteredResults.slice(indexOfFirstItem, indexOfLastItem);
	const totalPages = Math.ceil(filteredResults.length / itemsPerPage);

	useEffect(() => {
		if (currentPage > totalPages) {
			setCurrentPage(totalPages);
		}
	}, [filteredResults, totalPages, currentPage]);

	const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);

	return (
		<>
			<SubBanner bannerDetails={banner} alignEnd={false} />
			<Pagemenu menuItems={Tabs.tabs} />

			<section className="pt-120 bg-secondary center_bg_2">
				<div className={styles.first_block}>
					<div className="container">
						<h2 className="main_title text-center text-white mb-20" data-aos="fade-up">
							{pageData.acf.details_table.title}
						</h2>
						<div className={styles.first_content} data-aos="fade-up">
							<form className={styles.first_form}>
								<ul className={styles.first_form_list}>
									<li>
										<label>{pageData.acf.details_table.table_titles[0].title}</label>
										<SelectStyle options={names} placeholder={locale === 'ar' ? 'اختر' : 'Choose'} onChange={opt => setNameFilter(opt.value)} />
									</li>
									<li>
										<label>Serial Number</label>
										<SelectStyle options={serielnum} placeholder={locale === 'ar' ? 'اختر' : 'Choose'} onChange={opt => setSerielnumv(opt.value)} />
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[2].title}</label>
										<SelectStyle options={rating} placeholder={locale === 'ar' ? 'اختر' : 'Choose'} onChange={opt => setRatingv(opt.value)} />
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[3].title}</label>
										<SelectStyle options={ratingActions} placeholder={locale === 'ar' ? 'اختر' : 'Choose'} onChange={opt => setRatingActionsv(opt.value)} />
									</li>
									<li>
										<label>{pageData.acf.details_table.table_titles[4].title}</label>
										<SelectStyle options={outlooks} placeholder={locale === 'ar' ? 'اختر' : 'Choose'} onChange={opt => setOutlooksv(opt.value)} />
									</li>
									<li>
										<label>{locale === 'ar' ? "سنة الإصدار" : "Issued Year"}</label>
										<input type="text" onChange={(e) => setYears(e.target.value)} />
									</li>
									<li ref={resultsDivRef}>
										<button className={`${styles.btn_style_wrap} btn_style_wrap`} onClick={handleFilter}>
											<span className={`${langCode === "ar" ? "btn_style_primary_ar" : ""} btn_style_primary`}>Find a Rating</span>
											<span className={`${langCode === "ar" ? "btn_style_arrow_ar" : ""} ${"btn_style_arrow"}`}>
												<Image src={langCode === "ar" ? "/images/white_arrow_left.png" : "/images/white_right_arw.svg"} alt="icon" width={22} height={15} priority />
											</span>
										</button>
									</li>
								</ul>
							</form>
						</div>
					</div>
				</div>

				<div className="py-120">
					<div className="container">
						<div className={styles.table_style_second} data-aos="fade-up">
							{currentItems.length > 0 ? (
								<table className={styles.main_table_wrap}>
									<thead>
										<tr>
											{pageData.acf.details_table.table_titles.map((item, index) => (
												<th key={index} align={index <= 2 ? "left" : undefined}>{item.title}</th>
											))}
										</tr>
									</thead>
									<tbody>
										{currentItems.map((item, index) => (
											<tr key={index}>
												<td dangerouslySetInnerHTML={{ __html: item.title.rendered }} />
												<td>{item.acf.rating_type?.post_title}</td>
												<td>{item.acf.credit_rating?.long_term_rating}</td>
												<td>{item.acf.credit_rating?.short_term_rating}</td>
												<td>{item.acf.credit_rating?.outlook}</td>
												<td>{item.acf.credit_rating?.date_of_issuance}</td>
												<td style={{ textAlign: 'center' }}>
													{item.acf.press_release_?.url && (
														<a href={item.acf.press_release_.url} className={`${styles.pdf_link} text-primary`} target="_blank" download>
															<Image src="/images/dowload-primary.svg" alt="icon" width={15} height={15} priority />
														</a>
													)}
												</td>
												<td style={{ textAlign: 'center' }}>
													{item.acf.report?.url && (
														<a href={item.acf.report.url} className={`${styles.pdf_link} text-primary`} target="_blank" download>
															<Image src="/images/dowload-primary.svg" alt="icon" width={15} height={15} priority />
														</a>
													)}
												</td>
											</tr>
										))}
									</tbody>
								</table>
							) : (
								<p style={{ textAlign: 'center', width: '100%', color: 'white' }}>No Data Found</p>
							)}
						</div>

						{totalPages > 1 && (
							<div style={{ display: "flex", gap: "10px", justifyContent: "flex-end", paddingRight: "25px", marginTop: "20px" }}>
								{pageNumbers.map(number => (
									<button key={number} onClick={() => setCurrentPage(number)} style={{
										background: "transparent",
										color: currentPage === number ? "white" : "#787878",
										border: "unset"
									}}>
										{number}
									</button>
								))}
							</div>
						)}
					</div>
				</div>

				<div className="container">
					<Membercards />
				</div>
			</section>
		</>
	);
};

export default Index;

export async function getServerSideProps({ locale }) {
	const langCode = locale === "ar" ? "ar" : "en";
	try {
		const pageData = await fetchPageBySlug("pif-user-portal", langCode);
		const Tabs = await fetchRatingTab(langCode);
		const ratingList = await fetchCapclist(langCode, 100);
		const serviceList = await fetchServices(langCode);
		return {
			props: {
				pageData,
				Tabs,
				ratingList,
				serviceList,
			},
		};
	} catch (error) {
		console.error("Failed to fetch data:", error);
		return {
			props: {
				pageData: null,
			},
		};
	}
}
